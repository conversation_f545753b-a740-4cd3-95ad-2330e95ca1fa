<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Utang Invoice - PT. PUTERA WIBOWO BORNEO</title>
    <?php echo app('Illuminate\Foundation\Vite')([
        'resources/css/app.css'
    ]); ?>
    <style>
        html, body {
            height: 100vh;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            background-image:
                linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
                url('<?php echo e(asset('images/bg.png')); ?>');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            padding: clamp(0.3rem, 1vw, 0.8rem);
        }

        .header {
            flex-shrink: 0;
            width: 100%;
            max-width: 100%;
            padding: clamp(0.5rem, 2vw, 1rem);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding: clamp(0.5rem, 1vw, 1rem);
        }

        .invoice-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: clamp(10px, 2vw, 20px);
            padding: clamp(0.5rem, 2vw, 1.5rem);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .invoice-header {
            text-align: center;
            margin-bottom: clamp(0.5rem, 2vw, 1rem);
            flex-shrink: 0;
        }

        .invoice-title {
            color: var(--purple);
            font-size: clamp(1.2rem, 3vw, 2rem);
            font-weight: bold;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
        }

        .total-amount {
            color: var(--purple);
            font-size: clamp(1rem, 2.5vw, 1.5rem);
            font-weight: bold;
            margin-bottom: clamp(0.5rem, 1.5vw, 1rem);
        }

        .search-container {
            margin-bottom: clamp(0.5rem, 1.5vw, 1rem);
            flex-shrink: 0;
        }

        .search-input {
            width: 100%;
            padding: clamp(0.3rem, 1vw, 0.5rem);
            border: 2px solid #e0e7ff;
            border-radius: clamp(5px, 1vw, 10px);
            font-size: clamp(0.8rem, 2vw, 1rem);
            background: rgba(255, 255, 255, 0.9);
        }

        .table-container {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .table-wrapper {
            flex: 1;
            overflow: auto;
        }

        .table {
            font-size: clamp(0.6rem, 1.5vw, 0.8rem);
            margin-bottom: 0;
        }

        .table th {
            background-color: var(--purple);
            color: white;
            font-weight: bold;
            padding: clamp(0.3rem, 1vw, 0.5rem);
            border: none;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: clamp(0.2rem, 0.8vw, 0.4rem);
            vertical-align: middle;
            border-bottom: 1px solid #e0e7ff;
        }

        .status-badge {
            padding: clamp(0.1rem, 0.5vw, 0.2rem) clamp(0.3rem, 1vw, 0.5rem);
            border-radius: clamp(3px, 0.5vw, 5px);
            font-size: clamp(0.5rem, 1.2vw, 0.7rem);
            font-weight: bold;
        }

        .status-open {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .pagination-container {
            flex-shrink: 0;
            margin-top: clamp(0.5rem, 1vw, 1rem);
        }

        /* DataTables customization */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info {
            display: none;
        }

        .dataTables_wrapper .dataTables_paginate {
            text-align: center;
            margin-top: clamp(0.5rem, 1vw, 1rem);
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: clamp(0.2rem, 0.5vw, 0.3rem) clamp(0.4rem, 1vw, 0.6rem);
            margin: 0 clamp(1px, 0.2vw, 2px);
            border-radius: clamp(3px, 0.5vw, 5px);
            font-size: clamp(0.6rem, 1.2vw, 0.8rem);
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .table-responsive {
                font-size: clamp(0.5rem, 2vw, 0.7rem);
            }
            
            .table th, .table td {
                padding: clamp(0.1rem, 0.5vw, 0.3rem);
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="header">
        <div class="right-logo">
            <div class="logo-line">
                <img src="<?php echo e(asset('images/logo.png')); ?>" alt="Logo" />
                <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
            </div>
            <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="invoice-container">
            <div class="invoice-header">
                <h1 class="invoice-title">TOTAL INVOICE Rp. 800.000.000</h1>
            </div>

            <!-- Search -->
            <div class="search-container">
                <form method="GET" action="<?php echo e(route('invoice')); ?>">
                    <input type="text" name="search" class="search-input" placeholder="Cari Invoice, PO, atau Unit..." 
                           value="<?php echo e($search ?? ''); ?>">
                </form>
            </div>

            <!-- Table Container -->
            <div class="table-container">
                <div class="table-wrapper">
                    <table id="invoiceTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>NO</th>
                                <th>NAME</th>
                                <th>UNIT</th>
                                <th>STATUS</th>
                                <th>TANGGAL INVOICE</th>
                                <th>TOTAL</th>
                                <th>PEMBAYARAN</th>
                                <th>SISA</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $invoices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $invoice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($index + 1); ?></td>
                                <td>
                                    <strong><?php echo e($invoice['name']); ?></strong><br>
                                    <small>PO : <?php echo e($invoice['po']); ?></small>
                                </td>
                                <td><?php echo e($invoice['unit']); ?></td>
                                <td>
                                    <span class="status-badge status-open"><?php echo e($invoice['status']); ?></span>
                                </td>
                                <td><?php echo e($invoice['tanggal_invoice']); ?></td>
                                <td><strong><?php echo e($invoice['total']); ?></strong></td>
                                <td><?php echo e($invoice['pembayaran']); ?></td>
                                <td><strong><?php echo e($invoice['sisa']); ?></strong></td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <em>Tidak ada data invoice yang ditemukan.</em>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

      <!-- FLOATING HOME BUTTON -->
   <a href="<?php echo e(route('home')); ?>" class="card home-button neumorphism">
       <img class="imgicon-purple" src="<?php echo e(asset('assets/icon/home.png')); ?>" alt="Home" width="40" height="40">
       </img>
       <span>HOME</span>
   </a>
    

    <script>
        $(document).ready(function() {
            $('#invoiceTable').DataTable({
                "paging": true,
                "searching": false,
                "info": false,
                "ordering": true,
                "pageLength": 10,
                "lengthChange": false,
                "pagingType": "simple_numbers",
                "language": {
                    "paginate": {
                        "previous": "‹",
                        "next": "›"
                    },
                    "emptyTable": "Tidak ada data invoice yang tersedia"
                },
                "columnDefs": [
                    { "orderable": false, "targets": [0, 3] }
                ]
            });

            // Auto-submit search form on input
            $('input[name="search"]').on('input', function() {
                $(this).closest('form').submit();
            });
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\pwbapp\resources\views/invoice.blade.php ENDPATH**/ ?>